#!/usr/bin/env python3
"""
火山引擎网络连接测试脚本
用于诊断RTC API连接问题
"""

import asyncio
import httpx
import os
import sys
from datetime import datetime, timezone
import hashlib
import hmac
import urllib.parse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.settings import settings

async def test_basic_connectivity():
    """测试基本网络连接"""
    print("🔍 测试基本网络连接...")

    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 测试DNS解析和基本连接
            response = await client.get("https://rtc.volcengineapi.com",
                                      headers={"User-Agent": "VolcanoRTC-Test/1.0"})
            print(f"✅ 基本连接成功 - 状态码: {response.status_code}")
            return True
    except Exception as e:
        print(f"❌ 基本连接失败: {e}")
        return False

async def test_dns_resolution():
    """测试DNS解析"""
    print("🔍 测试DNS解析...")

    import socket
    try:
        # 测试DNS解析
        ip = socket.gethostbyname("rtc.volcengineapi.com")
        print(f"✅ DNS解析成功: rtc.volcengineapi.com -> {ip}")
        return True
    except Exception as e:
        print(f"❌ DNS解析失败: {e}")
        return False

def generate_official_signature(access_key: str, secret_key: str) -> dict:
    """基于官方代码生成签名"""
    from urllib.parse import quote

    # 使用UTC时间
    t = datetime.now(timezone.utc)
    x_date = t.strftime('%Y%m%dT%H%M%SZ')
    short_x_date = x_date[:8]

    # 请求参数
    method = "POST"
    host = "rtc.volcengineapi.com"
    path = "/"
    content_type = "application/json"
    query_params = {"Action": "StartVoiceChat", "Version": "2024-12-01"}
    body = '{"test": "connection"}'

    # 计算body hash
    x_content_sha256 = hashlib.sha256(body.encode('utf-8')).hexdigest()

    # 规范化查询字符串 - 按照官方代码
    def norm_query(params):
        query = ""
        for key in sorted(params.keys()):
            if type(params[key]) == list:
                for k in params[key]:
                    query = query + quote(key, safe="-_.~") + "=" + quote(k, safe="-_.~") + "&"
            else:
                query = query + quote(key, safe="-_.~") + "=" + quote(str(params[key]), safe="-_.~") + "&"
        query = query[:-1]
        return query.replace("+", "%20")

    canonical_querystring = norm_query(query_params)

    # 签名头部 - 按照官方代码顺序
    signed_headers_str = ";".join(["content-type", "host", "x-content-sha256", "x-date"])

    # 规范请求 - 完全按照官方代码格式
    canonical_request_str = "\n".join([
        method.upper(),
        path,
        canonical_querystring,
        "\n".join([
            "content-type:" + content_type,
            "host:" + host,
            "x-content-sha256:" + x_content_sha256,
            "x-date:" + x_date,
        ]),
        "",  # 空行！
        signed_headers_str,
        x_content_sha256,
    ])

    print("🔍 官方签名调试 - 规范请求:")
    print(canonical_request_str)
    print()

    # 计算hash
    hashed_canonical_request = hashlib.sha256(canonical_request_str.encode('utf-8')).hexdigest()
    print(f"🔍 规范请求Hash: {hashed_canonical_request}")

    # 凭证范围
    credential_scope = "/".join([short_x_date, "cn-north-1", "rtc", "request"])
    string_to_sign = "\n".join(["HMAC-SHA256", x_date, credential_scope, hashed_canonical_request])

    print(f"🔍 待签名字符串:")
    print(string_to_sign)
    print()

    # 计算签名 - 按照官方代码，不加VOLC前缀
    def hmac_sha256(key: bytes, content: str):
        return hmac.new(key, content.encode("utf-8"), hashlib.sha256).digest()

    k_date = hmac_sha256(secret_key.encode("utf-8"), short_x_date)
    k_region = hmac_sha256(k_date, "cn-north-1")
    k_service = hmac_sha256(k_region, "rtc")
    k_signing = hmac_sha256(k_service, "request")
    signature = hmac_sha256(k_signing, string_to_sign).hex()

    print(f"🔍 最终签名: {signature}")

    # 构建Authorization头
    authorization = f"HMAC-SHA256 Credential={access_key}/{credential_scope}, SignedHeaders={signed_headers_str}, Signature={signature}"

    return {
        "Host": host,
        "X-Content-Sha256": x_content_sha256,
        "X-Date": x_date,
        "Content-Type": content_type,
        "Authorization": authorization
    }

async def test_volcano_api_auth():
    """测试火山引擎API认证"""
    print("🔍 测试火山引擎API认证...")

    access_key = settings.VOLCANO_ACCESS_KEY_ID
    secret_key = settings.VOLCANO_SECRET_ACCESS_KEY

    if not access_key or not secret_key:
        print("❌ 缺少Access Key或Secret Key配置")
        return False

    print(f"📋 Access Key: {access_key[:10]}...")
    print(f"📋 Secret Key: {'*' * 20}")

    try:
        # 生成签名 - 使用官方方法
        headers = generate_official_signature(access_key, secret_key)

        # 测试API调用
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                "https://rtc.volcengineapi.com",
                params={"Action": "StartVoiceChat", "Version": "2024-12-01"},
                data='{"test": "connection"}',  # 使用data而不是json
                headers=headers
            )

            print(f"📡 API响应状态码: {response.status_code}")
            print(f"📡 API响应内容: {response.text[:200]}...")

            # 检查是否是认证错误
            if response.status_code == 403:
                print("❌ 认证失败 - 请检查Access Key和Secret Key")
                return False
            elif response.status_code == 400:
                print("✅ 认证成功 - 参数错误（这是预期的，因为我们发送的是测试数据）")
                return True
            else:
                print(f"✅ API连接成功 - 状态码: {response.status_code}")
                return True

    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始火山引擎RTC连接诊断...")
    print("=" * 50)

    # 测试步骤
    tests = [
        ("DNS解析", test_dns_resolution),
        ("基本连接", test_basic_connectivity),
        ("API认证", test_volcano_api_auth),
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
        print("-" * 30)

    # 总结
    print("\n📊 测试结果总结:")
    print("=" * 50)
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    print(f"\n总体结果: {success_count}/{total_count} 测试通过")

    if success_count == total_count:
        print("🎉 所有测试通过！RTC连接配置正常。")
    else:
        print("⚠️ 部分测试失败，请根据上述结果进行修复。")

if __name__ == "__main__":
    asyncio.run(main())
